"""
Unit tests for the MessageProcessor class.
"""

import pytest

from kn.message_processor import MessageProcessor, MessageValidationError


class TestMessageProcessor:
    """Test cases for MessageProcessor class."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.processor = MessageProcessor()
    
    def test_validate_message_valid(self):
        """Test that valid messages pass validation."""
        # Should not raise any exception - all contain "hello"
        self.processor.validate_message("Hello World")
        self.processor.validate_message("hello there")
        self.processor.validate_message("Say HELLO to everyone")
        self.processor.validate_message("hELLo with mixed case")
    
    def test_validate_message_empty_string(self):
        """Test that empty string raises MessageValidationError."""
        with pytest.raises(MessageValidationError) as exc_info:
            self.processor.validate_message("")

        assert str(exc_info.value) == "Message cannot be empty"
    
    def test_validate_message_whitespace_only(self):
        """Test that whitespace-only strings raise MessageValidationError for missing hello."""
        test_cases = [" ", "  ", "\t", "\n", " \t \n "]

        for whitespace_msg in test_cases:
            with pytest.raises(MessageValidationError) as exc_info:
                self.processor.validate_message(whitespace_msg)

            assert str(exc_info.value) == "Message must contain 'hello'"

    def test_validate_message_missing_hello(self):
        """Test that messages without 'hello' raise MessageValidationError."""
        test_cases = [
            "Good morning",
            "Test message",
            "123",
            "Special chars: !@#$%",
            "Hi there",
            "Goodbye world"
        ]

        for msg in test_cases:
            with pytest.raises(MessageValidationError) as exc_info:
                self.processor.validate_message(msg)

            assert str(exc_info.value) == "Message must contain 'hello'"
    
    def test_generate_response(self):
        """Test response generation."""
        test_cases = [
            "Hello World",
            "Test message",
            "123",
            "Special chars: !@#$%",
            "Unicode: 🚀 🎉"
        ]
        
        for msg in test_cases:
            response = self.processor.generate_response(msg)
            assert response == {"msg": msg}
            assert isinstance(response, dict)
            assert "msg" in response
    
    def test_process_echo_message_valid(self):
        """Test processing valid echo messages."""
        test_cases = [
            "Hello World",
            "hello there",
            "Say HELLO to everyone",
            "hELLo with mixed case",
            "Unicode hello: 🚀 🎉"
        ]

        for msg in test_cases:
            response = self.processor.process_echo_message(msg)
            assert response == {"msg": msg}
    
    def test_process_echo_message_empty(self):
        """Test processing empty messages raises MessageValidationError."""
        with pytest.raises(MessageValidationError) as exc_info:
            self.processor.process_echo_message("")

        assert str(exc_info.value) == "Message cannot be empty"
    
    def test_process_echo_message_whitespace(self):
        """Test processing whitespace-only messages raises MessageValidationError for missing hello."""
        test_cases = [" ", "  ", "\t", "\n", " \t \n "]

        for whitespace_msg in test_cases:
            with pytest.raises(MessageValidationError) as exc_info:
                self.processor.process_echo_message(whitespace_msg)

            assert str(exc_info.value) == "Message must contain 'hello'"

    def test_process_echo_message_missing_hello(self):
        """Test processing messages without 'hello' raises MessageValidationError."""
        test_cases = ["Good morning", "Test message", "Hi there"]

        for msg in test_cases:
            with pytest.raises(MessageValidationError) as exc_info:
                self.processor.process_echo_message(msg)

            assert str(exc_info.value) == "Message must contain 'hello'"
    
    def test_message_processor_initialization(self):
        """Test that MessageProcessor can be initialized."""
        processor = MessageProcessor()
        assert processor is not None
        assert isinstance(processor, MessageProcessor)
