"""
Integration tests for the FastAPI endpoints.
"""

import pytest
from fastapi.testclient import TestClient

from kn.main import app


class TestEchoAPI:
    """Test cases for the Echo API endpoints."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.client = TestClient(app)
    
    def test_root_endpoint(self):
        """Test the root endpoint returns service information."""
        response = self.client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["service"] == "Echo Service"
        assert data["version"] == "1.0.0"
        assert "endpoints" in data
        assert "/echo" in data["endpoints"]
    
    def test_echo_endpoint_valid_message(self):
        """Test echo endpoint with valid messages."""
        test_cases = [
            "Hello World",
            "hello there",
            "Say HELLO to everyone",
            "hELLo with mixed case",
            "Long hello message with multiple words and punctuation!"
        ]

        for msg in test_cases:
            response = self.client.get("/echo", params={"msg": msg})

            assert response.status_code == 200
            data = response.json()
            assert data == {"msg": msg}
    
    def test_echo_endpoint_empty_message(self):
        """Test echo endpoint with empty message."""
        response = self.client.get("/echo", params={"msg": ""})

        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "Message cannot be empty"
    
    def test_echo_endpoint_whitespace_message(self):
        """Test echo endpoint with whitespace-only messages."""
        test_cases = [" ", "  ", "\t"]  # Whitespace characters

        for whitespace_msg in test_cases:
            response = self.client.get("/echo", params={"msg": whitespace_msg})

            assert response.status_code == 400
            data = response.json()
            assert data["detail"] == "Message must contain 'hello'"

    def test_echo_endpoint_missing_hello(self):
        """Test echo endpoint with messages that don't contain 'hello'."""
        test_cases = ["Good morning", "Test message", "Hi there", "Goodbye world"]

        for msg in test_cases:
            response = self.client.get("/echo", params={"msg": msg})

            assert response.status_code == 400
            data = response.json()
            assert data["detail"] == "Message must contain 'hello'"

    def test_echo_endpoint_missing_parameter(self):
        """Test echo endpoint without msg parameter."""
        response = self.client.get("/echo")
        
        assert response.status_code == 422  # Unprocessable Entity
        data = response.json()
        assert "detail" in data
        # FastAPI automatically validates required query parameters
    
    def test_echo_endpoint_url_encoded_message(self):
        """Test echo endpoint with URL-encoded messages."""
        test_cases = [
            "Hello World",
            "hello with special chars!@#",
            "Multi word hello message"
        ]

        for msg in test_cases:
            response = self.client.get("/echo", params={"msg": msg})

            assert response.status_code == 200
            data = response.json()
            assert data == {"msg": msg}
    
    def test_echo_endpoint_response_headers(self):
        """Test that echo endpoint returns correct headers."""
        response = self.client.get("/echo", params={"msg": "hello test"})

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"


    def test_post_users(self):
        """Test POST /users"""
        response = self.client.post("/users", json={"username": "myuser", "email": "<EMAIL>"})

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.json().get("total_users") > 0
        assert response.json().get("message") == "User created"
    
    def test_api_documentation_endpoints(self):
        """Test that API documentation endpoints are accessible."""
        # Test OpenAPI schema
        response = self.client.get("/openapi.json")
        assert response.status_code == 200
        
        # Test Swagger UI
        response = self.client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = self.client.get("/redoc")
        assert response.status_code == 200
