import requests

deck_api = "https://www.deckofcardsapi.com/api/deck/"

# class Deck:
sort_order = {
    "ACE": 1,
    "2": 2,
    "3": 3,
    "4": 4,
    "5": 5,
    "6": 6,
    "7": 7,
    "8": 8,
    "9": 9,
    "10": 10,
    "JACK": 11,
    "QUEEN": 12,
    "KING": 13
}

class Pocker:

    def __init__(self):
        self.deck = None
        self.players = list()

    def new_deck(self):
        resp = requests.get(f"{deck_api}/new/shuffle/?deck_count=1")
        if resp.status_code != 200:
            raise Exception("Error creating deck")

        return resp.json()

    def draw(self, count=1):
        resp = requests.get(f"{deck_api}/{self.deck.get('deck_id')}/draw/?count={count}")
        if resp.status_code != 200:
            raise Exception("Error drawing cards")

        return resp.json()


    def sort(self, cards):
        return sorted(cards, key=lambda card: sort_order[card.get("value")] )


    def start(self=None, nplayers=2):
        self.deck = self.new_deck()
        # print(self.deck.get("deck_id"))
        for i in range(nplayers):
            self.players.append([])

        ncards = 0
        while True:
            for player in self.players:
                card = self.draw(1)
                if not card.get("success"):
                    return 0
                player.append(card.get("cards")[0])
                print(player)
            ncards += 1
            if ncards >= 5:
                # check for winner
               if winner := self.check_winner():
                print(f"Player with cards {winner} wins!")
                return winner


    def check_winner(self):

        straight_cards = 0
        last_card = -1
        player_number = 0
        for player_cards in self.players:
            player_number += 1
            cards = self.sort(player_cards)

            for card in cards:
                v = sort_order.get(card.get("value"))
                if v - last_card == 1:
                    straight_cards += 1
                elif v == last_card:
                    continue
                else:
                    straight_cards = 1
                last_card = v
                if straight_cards >= 5:
                    return player_number

        return 0


def main():
    """Main entry point for the application."""
    game = Pocker()
    res = game.start()
    print("Who won?", res)


if __name__ == "__main__":
    main()
