import sys
import re
import asyncio
import aiohttp

def extract_hrefs(html):
    href_pattern = re.compile(r'href\s*=\s*["\']?([^"\'\s>]+)', re.IGNORECASE)
    return {match.group(1) for match in href_pattern.finditer(html)}


async def fetch(session: aiohttp.ClientSession, url: str) -> str:
      async with session.get(url) as response:
        if response.status != 200:
            print(f"Failed to fetch {url}, status code: {response.status}")
            return None
        return await response.text()

async def crawl(base: str, depth: int = 1) -> None:

    q = asyncio.Queue()
    visited = set()
    q.put_nowait(base)
    d = 1
    async with aiohttp.ClientSession() as session:
        while not q.empty():
            size = q.qsize()
            print(f'Processing {size} urls at depth {d}')

            for _ in range(size):
                url = await q.get()
                print(f'Crawling {url} at depth {d}')
                visited.add(url)
                html = await fetch(session, url)
                if html:
                    for href in extract_hrefs(html):
                        if href.endswith('.css') or href.endswith('.js') or href.endswith('.svg') or href == '/':
                            continue
                        if not href.startswith('http'):
                            href = f'{base}{href}' if href.startswith('/') else f'{base}/{href}'
                        print(href)

                        if d < depth and href not in visited:
                            q.put_nowait(href)

            d += 1

async def main () -> None:
    if len(sys.argv) < 2:
        print(f"Usage: python webcrawler.py <url>")
        sys.exit(1)
    return await crawl(sys.argv[1], int(sys.argv[2]) if len(sys.argv) > 2 else 1)


if __name__ == '__main__':
    asyncio.run(main())
