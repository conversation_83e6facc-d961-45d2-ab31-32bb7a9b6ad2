"""
Message processing module for the Echo Service.
"""

from typing import Dict, Any


class MessageValidationError(ValueError):
    """Exception raised when message validation fails."""
    pass


class MessageProcessor:
    """
    Handles message processing and response generation for the echo service.
    """
    
    def __init__(self):
        """Initialize the MessageProcessor."""
        pass

    def validate_message(self, msg: str) -> None:
        """
        Validate that the message is not empty and contains "hello".

        Args:
            msg: The message to validate

        Raises:
            MessageValidationError: If msg is empty, contains only whitespace, or doesn't contain "hello"
        """
        # First check if message is empty or whitespace
        if not msg:
            raise MessageValidationError("Message cannot be empty")

        # Then check if it contains "hello" (case-insensitive)
        if "hello" not in msg.lower():
            raise MessageValidationError("Message must contain 'hello'")

    def process_echo_message(self, msg: str) -> Dict[str, Any]:
        """
        Process an echo message request.

        Args:
            msg: The message to echo back

        Returns:
            Dictionary containing the echoed message

        Raises:
            MessageValidationError: If msg is empty
        """
        self.validate_message(msg)
        return self.generate_response(msg)
    
    def generate_response(self, msg: str) -> Dict[str, Any]:
        """
        Generate the response dictionary for an echo message.
        
        Args:
            msg: The message to include in the response
            
        Returns:
            Dictionary with the message
        """
        return {"msg": msg}
