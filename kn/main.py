from fastapi import <PERSON><PERSON><PERSON>, Query, HTTPException, Body
from typing import Optional

from kn.message_processor import MessageProcessor, MessageValidationError
from kn.user import User

# Create FastAPI app instance
app = FastAPI(
    title="Echo Service",
    description="A simple FastAPI service with an echo endpoint",
    version="1.0.0"
)

# Create message processor instance
message_processor = MessageProcessor()

@app.get("/echo")
async def echo_message(msg: str = Query(..., description="Message to echo back")):
    """
    Echo endpoint that accepts a message and returns it as JSON.

    Args:
        msg: The message to echo back

    Returns:
        JSON object with the echoed message

    Raises:
        HTTPException: 400 if msg is empty
    """
    try:
        return message_processor.process_echo_message(msg)
    except MessageValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/")
async def root():
    """
    Root endpoint with basic service information.
    """
    return {
        "service": "Echo Service",
        "version": "1.0.0",
        "endpoints": {
            "/echo": "GET - Echo a message back as J<PERSON><PERSON>"
        }
    }

users = []

@app.post("/users")
async def create_user(user: User = Body(..., description="User information")):
    users.append(user)
    return {"message": "User created", "total_users": len(users)}


def main():
    """Main entry point for the application."""
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    main()
