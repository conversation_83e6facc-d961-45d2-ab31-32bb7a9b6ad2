# Echo Service

A simple FastAPI service with an echo endpoint that accepts a message and returns it as JSON.

## Prerequisites

- Python 3.8 or higher
- uv (Ultra-fast Python package installer and resolver)

Install uv if you haven't already:
```bash
pip install uv
```

## Installation

1. Install dependencies using uv:
```bash
uv sync
```

2. For development dependencies (includes testing tools):
```bash
uv sync --dev
```

## Running the Service

### Option 1: Using Python module directly with uv
```bash
uv run python -m kn.main
```

### Option 2: Using uvicorn directly with uv
```bash
uv run uvicorn kn.main:app --host 0.0.0.0 --port 8000 --reload
```

### Option 3: Using the installed script (after uv sync)
```bash
echo-start
```

The service will be available at `http://localhost:8000`

## Testing

The project includes comprehensive unit and integration tests.

### Running Tests

**Basic test run:**
```bash
uv run pytest
```

**Verbose output:**
```bash
uv run pytest -v
```

**With coverage report:**
```bash
uv run pytest --cov=kn --cov-report=html --cov-report=term
```

**Run specific test file:**
```bash
uv run pytest tests/test_message_processor.py
```

**Run with custom pytest options:**
```bash
uv run pytest -v --tb=short
```

**Using the test runner script:**
```bash
python run_tests.py
```

### Test Structure

- `tests/test_message_processor.py` - Unit tests for the MessageProcessor class
- `tests/test_api.py` - Integration tests for FastAPI endpoints
- `tests/conftest.py` - Shared pytest fixtures and configuration

### Test Coverage

The tests cover:
- ✅ Message validation (empty, whitespace, missing "hello", valid messages)
- ✅ Response generation
- ✅ HTTP status codes (200, 400, 422)
- ✅ API endpoint functionality
- ✅ Error handling
- ✅ URL encoding/decoding
- ✅ API documentation endpoints

## API Endpoints

### GET /echo
Echo a message back as JSON.

**Parameters:**
- `msg` (required): The message to echo back (cannot be empty and must contain "hello")

**Example:**
```bash
curl "http://localhost:8000/echo?msg=Hello%20World"
```

**Response (Success - 200):**
```json
{
  "msg": "Hello World"
}
```

**Response (Error - 400 - Empty message):**
```bash
curl "http://localhost:8000/echo?msg="
```
```json
{
  "detail": "Message cannot be empty"
}
```

**Response (Error - 400 - Missing "hello"):**
```bash
curl "http://localhost:8000/echo?msg=Good%20morning"
```
```json
{
  "detail": "Message must contain 'hello'"
}
```

### GET /
Root endpoint with service information.

## API Documentation

Once the service is running, you can access:
- Interactive API docs: `http://localhost:8000/docs`
- Alternative API docs: `http://localhost:8000/redoc`
