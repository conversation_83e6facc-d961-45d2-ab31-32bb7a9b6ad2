[project]
name = "commure"
version = "1.0.0"
description = "A simple FastAPI service with an echo endpoint"
authors = [
    {name = "Develo<PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.104.1",
    "requests>=2.32.4",
    "uvicorn[standard]>=0.24.0",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[project.urls]
Homepage = "https://github.com/kos2nov/commure"
Repository = "https://github.com/kos2nov/commure.git"


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["kn"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "httpx>=0.24.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
]

[project.scripts]
echo-start = "kn.main:main"



[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--strict-config",
    "--disable-warnings"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests"
]
